require 'application_system_test_case'

class ScoutApplicantsFilteringTest < ApplicationSystemTestCase
  def setup
    # Clean up any existing data
    JobApplication.destroy_all
    Job.destroy_all
    BadgeAssignment.destroy_all
    ImpersonationLog.destroy_all
    OrganizationMembership.destroy_all
    Organization.destroy_all
    User.destroy_all

    # Create scout user
    @scout =
      User.create!(
        email: '<EMAIL>',
        password: 'password123456',
        first_name: 'Scout',
        last_name: 'Test',
        scout_signup_completed: true,
        verified: true,
        onboarding_completed: true,
        onboarding_step: 'completed',
      )

    # Create talent users
    @talent_reviewed =
      User.create!(
        email: '<EMAIL>',
        password: 'password123456',
        first_name: 'Reviewed',
        last_name: 'Talent',
        talent_signup_completed: true,
        verified: true,
      )

    @talent_qualified =
      User.create!(
        email: '<EMAIL>',
        password: 'password123456',
        first_name: 'Qualified',
        last_name: 'Talent',
        talent_signup_completed: true,
        verified: true,
      )

    @talent_accepted =
      User.create!(
        email: '<EMAIL>',
        password: 'password123456',
        first_name: 'Accepted',
        last_name: 'Talent',
        talent_signup_completed: true,
        verified: true,
      )

    # Create organization and associate scout
    @organization = Organization.create!(name: "Scout's Test Org")
    OrganizationMembership.create!(
      user: @scout,
      organization: @organization,
      org_role: 'admin',
    )
    @scout.update!(last_logged_in_organization_id: @organization.id)

    # Create jobs
    @job1 =
      Job.create!(
        title: 'Test Job 1',
        description: 'Test job description 1',
        job_category: 'social_media',
        platform: 'linkedin',
        outcome: 'leads',
        social_media_goal_type: 'social_media_leads',
        social_media_understands_risk_acknowledged: '1',
        budget_range: 'range_1000_2000',
        work_duration: 'one_time_project',
        status: 'published',
        organization: @organization,
      )

    @job2 =
      Job.create!(
        title: 'Test Job 2',
        description: 'Test job description 2',
        job_category: 'newsletter',
        outcome: 'grow_email_list',
        newsletter_frequency: 'weekly',
        newsletter_length: 'words_300_600',
        budget_range: 'range_1000_2000',
        work_duration: 'long_term',
        status: 'published',
        organization: @organization,
      )

    # Create job applications with different statuses
    @application_reviewed =
      JobApplication.create!(
        user: @talent_reviewed,
        job: @job1,
        application_letter: 'Test application letter for reviewed',
        status: 'reviewed',
        applied_at: 1.day.ago,
      )

    @application_qualified =
      JobApplication.create!(
        user: @talent_qualified,
        job: @job1,
        application_letter: 'Test application letter for qualified',
        status: 'qualified',
        applied_at: 2.days.ago,
      )

    @application_accepted =
      JobApplication.create!(
        user: @talent_accepted,
        job: @job2,
        application_letter: 'Test application letter for accepted',
        status: 'accepted',
        applied_at: 3.days.ago,
      )

    # Ensure applications are indexed in Elasticsearch
    JobApplication.reindex
    sleep 1 # Give Elasticsearch time to index
  end

  def teardown
    JobApplication.destroy_all
    Job.destroy_all
    BadgeAssignment.destroy_all
    ImpersonationLog.destroy_all
    OrganizationMembership.destroy_all
    Organization.destroy_all
    User.destroy_all
  end

  def sign_in_as_scout
    visit sign_in_path
    fill_in 'Email', with: @scout.email
    fill_in 'Password', with: 'password123456'
    click_button 'Log in'

    # Wait for successful login - should be redirected to launchpad
    assert_text 'LAUNCHPAD', wait: 10
  end

  test 'displays all applications by default' do
    sign_in_as_scout
    visit scout_applicants_path

    # Should show all three applications
    assert_text 'Reviewed Talent'
    assert_text 'Qualified Talent'
    assert_text 'Accepted Talent'

    # Should show correct counts in status buttons (numbers and labels are separate)
    assert_text '3'
    assert_text 'Total'
    assert_text '1'
    assert_text 'Reviewed'
    assert_text 'Qualified'
    assert_text 'Accepted'
  end

  test 'filters applications by clicking reviewed status button' do
    sign_in_as_scout
    visit scout_applicants_path

    # Click the Reviewed status button (find link containing "Reviewed" text)
    find('a', text: 'Reviewed').click

    # Debug: Check current URL
    puts "Current URL after click: #{page.current_url}"

    # Should navigate to filtered URL
    assert_current_path scout_applicants_path
    assert page.current_url.include?('status=reviewed')

    # Should show only reviewed application
    assert_text 'Reviewed Talent'
    assert_no_text 'Qualified Talent'
    assert_no_text 'Accepted Talent'

    # Reviewed button should be selected (appears with different styling)
    assert_selector '.bg-stone-200', text: 'Reviewed'
  end

  test 'filters applications by clicking qualified status button' do
    sign_in_as_scout
    visit scout_applicants_path

    # Click the Qualified status button
    find('a', text: 'Qualified').click

    # Should navigate to filtered URL
    assert_current_path scout_applicants_path
    assert page.current_url.include?('status=qualified')

    # Should show only qualified application
    assert_no_text 'Reviewed Talent'
    assert_text 'Qualified Talent'
    assert_no_text 'Accepted Talent'

    # Qualified button should be selected
    assert_selector '.bg-stone-200', text: 'Qualified'
  end

  test 'filters applications by clicking accepted status button' do
    sign_in_as_scout
    visit scout_applicants_path

    # Click the Accepted status button
    find('a', text: 'Accepted').click

    # Should navigate to filtered URL
    assert_current_path scout_applicants_path
    assert page.current_url.include?('status=accepted')

    # Should show only accepted application
    assert_no_text 'Reviewed Talent'
    assert_no_text 'Qualified Talent'
    assert_text 'Accepted Talent'
  end

  test 'clears filter by clicking total button' do
    sign_in_as_scout

    # Start with a filtered view
    visit scout_applicants_path(status: 'reviewed')

    # Should show only reviewed application
    assert_text 'Reviewed Talent'
    assert_no_text 'Qualified Talent'
    assert_no_text 'Accepted Talent'

    # Click the Total button to clear filter
    find('a', text: 'Total').click

    # Should navigate to unfiltered URL
    assert_current_path scout_applicants_path
    refute page.current_url.include?('status=')

    # Should show all applications again
    assert_text 'Reviewed Talent'
    assert_text 'Qualified Talent'
    assert_text 'Accepted Talent'
  end

  test 'clears filter by clicking selected status button' do
    sign_in_as_scout

    # Start with a filtered view
    visit scout_applicants_path(status: 'reviewed')

    # Should show only reviewed application
    assert_text 'Reviewed Talent'
    assert_no_text 'Qualified Talent'

    # Click the selected Reviewed button to clear filter
    find('a', text: 'Reviewed').click

    # Should navigate to unfiltered URL
    assert_current_path scout_applicants_path
    refute page.current_url.include?('status=')

    # Should show all applications again
    assert_text 'Reviewed Talent'
    assert_text 'Qualified Talent'
    assert_text 'Accepted Talent'
  end

  test 'direct navigation to filtered URL works correctly' do
    sign_in_as_scout

    # Navigate directly to filtered URL
    visit scout_applicants_path(status: 'qualified')

    # Should show only qualified application
    assert_no_text 'Reviewed Talent'
    assert_text 'Qualified Talent'
    assert_no_text 'Accepted Talent'

    # URL should contain the status parameter
    assert page.current_url.include?('status=qualified')

    # Qualified button should be selected
    assert_selector '.bg-stone-200', text: 'Qualified'
  end

  test 'browser back and forward buttons work with filtering' do
    sign_in_as_scout
    visit scout_applicants_path

    # Apply a filter
    find('a', text: 'Reviewed').click
    assert page.current_url.include?('status=reviewed')
    assert_text 'Reviewed Talent'
    assert_no_text 'Qualified Talent'

    # Apply another filter
    find('a', text: 'Qualified').click
    assert page.current_url.include?('status=qualified')
    assert_no_text 'Reviewed Talent'
    assert_text 'Qualified Talent'

    # Use browser back button
    page.go_back
    assert page.current_url.include?('status=reviewed')
    assert_text 'Reviewed Talent'
    assert_no_text 'Qualified Talent'

    # Use browser forward button
    page.go_forward
    assert page.current_url.include?('status=qualified')
    assert_no_text 'Reviewed Talent'
    assert_text 'Qualified Talent'
  end
end
