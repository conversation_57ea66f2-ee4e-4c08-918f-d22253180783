require 'test_helper'

class Scout::ApplicantsControllerTest < ActionDispatch::IntegrationTest
  def setup
    # Clean up any existing data
    JobApplication.destroy_all
    Job.destroy_all
    BadgeAssignment.destroy_all
    ImpersonationLog.destroy_all
    OrganizationMembership.destroy_all
    Organization.destroy_all
    User.destroy_all

    # Create scout user
    @scout =
      User.create!(
        email: '<EMAIL>',
        password: 'password123456',
        first_name: 'Scout',
        last_name: 'Test',
        scout_signup_completed: true,
        verified: true,
        onboarding_completed: true,
        onboarding_step: 'completed',
      )

    # Create talent users
    @talent1 =
      User.create!(
        email: '<EMAIL>',
        password: 'password123456',
        first_name: 'Talent',
        last_name: 'One',
        talent_signup_completed: true,
        verified: true,
      )

    @talent2 =
      User.create!(
        email: '<EMAIL>',
        password: 'password123456',
        first_name: 'Talent',
        last_name: 'Two',
        talent_signup_completed: true,
        verified: true,
      )

    @talent3 =
      User.create!(
        email: '<EMAIL>',
        password: 'password123456',
        first_name: 'Talent',
        last_name: 'Three',
        talent_signup_completed: true,
        verified: true,
      )

    # Create organization and associate scout
    @organization = Organization.create!(name: "Scout's Test Org")
    OrganizationMembership.create!(
      user: @scout,
      organization: @organization,
      org_role: 'admin',
    )
    @scout.update!(last_logged_in_organization_id: @organization.id)

    # Create jobs
    @job1 =
      Job.create!(
        title: 'Test Job 1',
        description: 'Test job description 1',
        job_category: 'social_media',
        platform: 'linkedin',
        outcome: 'leads',
        social_media_goal_type: 'social_media_leads',
        social_media_understands_risk_acknowledged: '1',
        budget_range: 'range_1000_2000',
        work_duration: 'one_time_project',
        status: 'published',
        organization: @organization,
      )

    @job2 =
      Job.create!(
        title: 'Test Job 2',
        description: 'Test job description 2',
        job_category: 'lead_magnet',
        outcome: 'grow_email_list',
        budget_range: 'range_1000_2000',
        work_duration: 'ongoing',
        status: 'published',
        organization: @organization,
      )

    # Create job applications with different statuses
    @application_reviewed =
      JobApplication.create!(
        user: @talent1,
        job: @job1,
        application_letter: 'Test application letter',
        status: 'reviewed',
        applied_at: 1.day.ago,
      )

    @application_qualified =
      JobApplication.create!(
        user: @talent2,
        job: @job1,
        application_letter: 'Test application letter',
        status: 'qualified',
        applied_at: 2.days.ago,
      )

    @application_accepted =
      JobApplication.create!(
        user: @talent3,
        job: @job2,
        application_letter: 'Test application letter',
        status: 'accepted',
        applied_at: 3.days.ago,
      )

    # Sign in as scout
    post sign_in_url,
         params: {
           email: @scout.email,
           password: 'password123456',
         }
  end

  def teardown
    JobApplication.destroy_all
    Job.destroy_all
    BadgeAssignment.destroy_all
    ImpersonationLog.destroy_all
    OrganizationMembership.destroy_all
    Organization.destroy_all
    User.destroy_all
  end

  test 'should get index without status filter and show all applications' do
    get scout_applicants_path

    assert_response :success
    assert_includes response.body, @talent1.name.full # reviewed
    assert_includes response.body, @talent2.name.full # qualified
    assert_includes response.body, @talent3.name.full # accepted
  end

  test 'should filter by reviewed status' do
    get scout_applicants_path, params: { status: 'reviewed' }

    assert_response :success
    assert_includes response.body, @talent1.name.full # reviewed
    assert_not_includes response.body, @talent2.name.full # qualified
    assert_not_includes response.body, @talent3.name.full # accepted
  end

  test 'should filter by qualified status' do
    get scout_applicants_path, params: { status: 'qualified' }

    assert_response :success
    assert_not_includes response.body, @talent1.name.full # reviewed
    assert_includes response.body, @talent2.name.full # qualified
    assert_not_includes response.body, @talent3.name.full # accepted
  end

  test 'should filter by accepted status' do
    get scout_applicants_path, params: { status: 'accepted' }

    assert_response :success
    assert_not_includes response.body, @talent1.name.full # reviewed
    assert_not_includes response.body, @talent2.name.full # qualified
    assert_includes response.body, @talent3.name.full # accepted
  end

  test 'should handle non-existent status gracefully' do
    get scout_applicants_path, params: { status: 'non_existent' }

    assert_response :success

    # Should show no applications for non-existent status
    assert_not_includes response.body, @talent1.name.full
    assert_not_includes response.body, @talent2.name.full
    assert_not_includes response.body, @talent3.name.full
  end

  test 'should display correct application counts in status buttons' do
    get scout_applicants_path

    assert_response :success

    # Check that status counts are displayed correctly
    assert_includes response.body, '3 Total' # All applications
    assert_includes response.body, '1 Reviewed' # One reviewed application
    assert_includes response.body, '1 Qualified' # One qualified application
    assert_includes response.body, '1 Accepted' # One accepted application
  end

  test 'should show correct button selection state for filtered status' do
    get scout_applicants_path, params: { status: 'reviewed' }

    assert_response :success

    # The reviewed button should link to clear the filter (no status param)
    assert_select 'a[href=?]', scout_applicants_path, text: /Reviewed/

    # Other status buttons should link to their respective filters
    assert_select 'a[href=?]',
                  scout_applicants_path(status: 'qualified'),
                  text: /Qualified/
    assert_select 'a[href=?]',
                  scout_applicants_path(status: 'accepted'),
                  text: /Accepted/
  end

  test 'should combine status filter with job filter' do
    get scout_applicants_path, params: { status: 'reviewed', job_id: @job1.id }

    assert_response :success
    assert_includes response.body, @talent1.name.full # reviewed application for job1
    assert_not_includes response.body, @talent2.name.full # qualified (different status)
    assert_not_includes response.body, @talent3.name.full # accepted (different job)
  end

  test 'should handle empty status parameter' do
    get scout_applicants_path, params: { status: '' }

    assert_response :success

    # Empty status should show all applications
    assert_includes response.body, @talent1.name.full
    assert_includes response.body, @talent2.name.full
    assert_includes response.body, @talent3.name.full
  end
end
